# Admin Dashboard - E-commerce Store Management

A modern, comprehensive admin dashboard for e-commerce store management built with Next.js 14, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Tech Stack**: Next.js 14 with App Router, TypeScript, Tailwind CSS
- **UI Components**: Shadcn/ui for consistent and accessible components
- **State Management**: Zustand for lightweight state management
- **Data Fetching**: TanStack Query for efficient data fetching and caching
- **Forms**: React Hook Form with Zod validation
- **Tables**: TanStack Table for advanced data tables
- **Icons**: Lucide React for beautiful icons
- **Notifications**: React Hot Toast for user feedback
- **Date Handling**: date-fns for date manipulation

## 📁 Project Structure

```
src/
├── app/
│   ├── admin/              # Admin panel pages
│   │   ├── dashboard/      # Dashboard page
│   │   └── layout.tsx      # Admin layout
│   ├── globals.css         # Global styles
│   └── layout.tsx          # Root layout
├── components/
│   ├── ui/                 # Reusable UI components
│   ├── admin/              # Admin-specific components
│   └── charts/             # Chart components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility libraries
├── stores/                 # Zustand stores
├── types/                  # TypeScript type definitions
└── utils/                  # Utility functions
```

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd admin-dashboard
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env.local
```

4. Update environment variables in `.env.local` with your configuration.

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000/admin/dashboard](http://localhost:3000/admin/dashboard) to view the admin dashboard.

## 📜 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run type-check` - Run TypeScript type checking
- `npm run clean` - Clean build artifacts

## 🎯 Development Phases

This project is being developed in 10 phases:

1. ✅ **Phase 1**: Environment Setup & Basic Structure
2. 🔄 **Phase 2**: Design System & Basic Components
3. 📋 **Phase 3**: Admin Layout & Navigation
4. 🔐 **Phase 4**: Authentication & Permissions
5. 📊 **Phase 5**: Dashboard & Analytics
6. 📦 **Phase 6**: Product Management
7. 🛒 **Phase 7**: Order & Customer Management
8. 📝 **Phase 8**: Content & Settings Management
9. 📈 **Phase 9**: Reports & Analytics
10. 🚀 **Phase 10**: Testing, Optimization & Deployment

## 🔧 Configuration

### Environment Variables

See `.env.example` for all available environment variables:

- `DATABASE_URL` - Database connection string
- `NEXTAUTH_SECRET` - Authentication secret
- `API_BASE_URL` - API base URL
- `ADMIN_EMAIL` - Default admin email
- And more...

### TypeScript Paths

The project uses TypeScript path mapping for clean imports:

- `@/*` - src/*
- `@/components/*` - src/components/*
- `@/ui/*` - src/components/ui/*
- `@/hooks/*` - src/hooks/*
- `@/lib/*` - src/lib/*
- `@/stores/*` - src/stores/*
- `@/types/*` - src/types/*
- `@/utils/*` - src/utils/*

## 🎨 UI Components

This project uses Shadcn/ui components with Tailwind CSS for styling. Components are located in `src/components/ui/` and can be customized as needed.

## 📱 Responsive Design

The dashboard is fully responsive and works on:
- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🔒 Security Features

- CSRF protection
- XSS protection
- Secure headers configuration
- Role-based access control (planned)
- Input validation with Zod

## 🚀 Deployment

The application can be deployed on:

- **Vercel** (recommended)
- **Netlify**
- **Docker**
- **Traditional hosting**

For Vercel deployment:
```bash
npm run build
vercel --prod
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the development phases plan
