# خطة تطوير الواجهة الأمامية الشاملة - متجر إلكتروني متقدم (نسخة محسنة من سلة)

## 📋 نظرة عامة على المشروع

### الهدف الرئيسي
تطوير واجهة أمامية متقدمة لمتجر إلكتروني باستخدام Next.js مع TypeScript، تتميز بتجربة مستخدم استثنائية وأداء عالي، مع دعم كامل للتجارة الإلكترونية الحديثة.

### التقنيات المستخدمة
- **إطار العمل:** Next.js 13+ مع App Router
- **لغة البرمجة:** TypeScript
- **مكتبة الواجهة:** React 18+
- **التصميم:** Tailwind CSS
- **إدارة الحالة:** React Context API / Redux Toolkit
- **جلب البيانات:** SWR / React Query
- **تطوير المكونات:** Storybook
- **الاختبارات:** React Testing Library + Cypress/Playwright
- **النشر:** Vercel

---

## 🏗️ الهيكل المعماري للواجهة الأمامية

### بنية المجلدات المقترحة
```
src/
├── components/           # المكونات القابلة لإعادة الاستخدام
│   ├── atoms/           # المكونات الذرية (Button, Input, etc.)
│   ├── molecules/       # الجزيئات (SearchBar, ProductCard, etc.)
│   ├── organisms/       # الكائنات المعقدة (Header, Footer, etc.)
│   └── templates/       # قوالب الصفحات
├── pages/              # صفحات Next.js
├── hooks/              # Custom React Hooks
├── services/           # خدمات API
├── store/              # إدارة الحالة العامة
├── utils/              # وظائف مساعدة
├── types/              # تعريفات TypeScript
└── styles/             # ملفات التصميم
```

### نمط التصميم الذري (Atomic Design)
- **Atoms:** Button, Input, Typography, Icon
- **Molecules:** SearchBar, ProductCard, FormField, CartItem
- **Organisms:** Header, Footer, ProductGrid, CheckoutForm
- **Templates:** PageLayout, AdminLayout
- **Pages:** HomePage, ProductPage, CartPage, etc.

---

## 📅 خطة التطوير التفصيلية (20 أسبوع)

## المرحلة الأولى: الأساسيات ونظام التصميم (أسابيع 1-4)

### الأسبوع 1: إعداد المشروع الأساسي
**الأهداف:**
- تهيئة بيئة التطوير الكاملة
- إعداد أدوات الجودة والتطوير

**المهام التفصيلية:**
- [ ] إنشاء مشروع Next.js جديد مع TypeScript
- [ ] تكوين ESLint و Prettier للحفاظ على جودة الكود
- [ ] إعداد Husky و lint-staged للتحقق قبل الـ commit
- [ ] تهيئة Storybook للتطوير المعزول للمكونات
- [ ] إعداد GitHub Actions للـ CI/CD
- [ ] تكوين Tailwind CSS مع ملف theme مخصص
- [ ] إعداد متغيرات البيئة والتكوين الأساسي

### الأسبوع 2: نظام التصميم الأساسي
**الأهداف:**
- بناء المكونات الأساسية القابلة لإعادة الاستخدام
- إنشاء نظام تصميم متسق

**المهام التفصيلية:**
- [ ] تطوير مكونات Atoms في Storybook:
  - Button (مع variants مختلفة)
  - Input (text, email, password, number)
  - Typography (headings, paragraphs, labels)
  - Icon (مع مكتبة أيقونات)
  - Card (container أساسي)
  - Badge/Tag
  - Spinner/Loading
- [ ] إنشاء نظام ألوان وخطوط متسق
- [ ] تطوير مكونات Molecules:
  - SearchBar
  - ProductCard الأساسي
  - FormField (Input + Label + Error)
  - Pagination
- [ ] كتابة stories لكل مكون في Storybook

### الأسبوع 3: التخطيط العام والمصادقة
**الأهداف:**
- بناء هيكل الموقع الأساسي
- تطوير نظام المصادقة

**المهام التفصيلية:**
- [ ] تطوير مكونات Organisms:
  - Header (مع navigation وسلة التسوق)
  - Footer (مع روابط ومعلومات الاتصال)
  - Layout الرئيسي
  - MobileMenu للهواتف المحمولة
- [ ] إنشاء صفحات المصادقة:
  - `/login` - صفحة تسجيل الدخول
  - `/register` - صفحة إنشاء حساب جديد
  - `/forgot-password` - استعادة كلمة المرور
- [ ] تطوير نماذج المصادقة باستخدام React Hook Form
- [ ] إعداد خدمة API مركزية باستخدام Axios
- [ ] تطوير React Context لإدارة حالة المصادقة
- [ ] تطبيق حماية المسارات (Protected Routes)

### الأسبوع 4: عرض المحتوى والتدويل
**الأهداف:**
- ربط الواجهة بالـ API
- إعداد دعم متعدد اللغات

**المهام التفصيلية:**
- [ ] إعداد SWR أو React Query لجلب البيانات
- [ ] تطوير الصفحة الرئيسية (`/`):
  - Hero Banner متحرك
  - عرض الفئات المميزة
  - عرض المنتجات المميزة
  - قسم المنتجات الجديدة
- [ ] إنشاء صفحة فئة ديناميكية (`/category/[slug]`)
- [ ] ربط الواجهة مع Headless CMS للبنرات الإعلانية
- [ ] إعداد next-i18next لدعم العربية والإنجليزية
- [ ] تطوير مكون LanguageSwitcher
- [ ] إنشاء ملفات الترجمة الأساسية

---

## المرحلة الثانية: وظائف التجارة الإلكترونية الأساسية (أسابيع 5-8)

### الأسبوع 5: صفحة تفاصيل المنتج
**الأهداف:**
- تطوير صفحة منتج شاملة ومتقدمة
- تحسين تجربة عرض المنتجات

**المهام التفصيلية:**
- [ ] إنشاء صفحة ديناميكية (`/product/[slug]`)
- [ ] تطوير معرض صور المنتج:
  - عرض متعدد الصور مع thumbnails
  - إمكانية التكبير (zoom)
  - دعم الفيديوهات
- [ ] عرض معلومات المنتج الكاملة:
  - الاسم والوصف
  - السعر مع العروض
  - متوسط التقييم ونجوم العرض
  - معلومات المخزون
- [ ] استخدام getServerSideProps لـ SEO والبيانات الحديثة
- [ ] تطوير مكون Breadcrumb للتنقل
- [ ] إضافة مكون "منتجات مشابهة"

### الأسبوع 6: دعم متغيرات المنتج
**الأهداف:**
- تطوير نظام اختيار متغيرات المنتج
- تحديث ديناميكي للأسعار والمخزون

**المهام التفصيلية:**
- [ ] تطوير مكونات اختيار الخيارات:
  - ColorPicker (دوائر ألوان)
  - SizeSelector (أزرار المقاسات)
  - VariantSelector عام
- [ ] تطبيق منطق تحديث البيانات بناءً على الاختيار:
  - تحديث السعر
  - تغيير الصورة المعروضة
  - عرض حالة المخزون
- [ ] تعطيل الخيارات غير المتوفرة
- [ ] تطوير مكون QuantitySelector
- [ ] إضافة validation للاختيارات المطلوبة
- [ ] تحسين UX مع loading states

### الأسبوع 7: بناء واجهة سلة التسوق
**الأهداف:**
- تطوير صفحة سلة تسوق شاملة
- تصميم تجربة مستخدم سلسة

**المهام التفصيلية:**
- [ ] إنشاء صفحة السلة (`/cart`)
- [ ] تطوير مكونات السلة:
  - CartItem (عرض منتج في السلة)
  - QuantityUpdater
  - RemoveButton
  - OrderSummary
- [ ] تطوير واجهة تحديث الكمية:
  - أزرار + و -
  - حقل إدخال مباشر
  - تحديث فوري
- [ ] إضافة حقل كوبون الخصم (UI فقط)
- [ ] تطوير مكون EmptyCart للسلة الفارغة
- [ ] إضافة تأكيدات الحذف
- [ ] تحسين الاستجابة للأجهزة المختلفة

### الأسبوع 8: ربط السلة بالـ API
**الأهداف:**
- ربط جميع وظائف السلة بالخادم
- إدارة حالة السلة عبر التطبيق

**المهام التفصيلية:**
- [ ] تطوير خدمات API للسلة:
  - addToCart
  - updateCartItem
  - removeFromCart
  - getCart
- [ ] ربط زر "إضافة للسلة" في صفحة المنتج
- [ ] تطوير React Context لإدارة حالة السلة:
  - عدد المنتجات
  - إجمالي السعر
  - تحديث فوري عبر التطبيق
- [ ] إضافة إشعارات النجاح والخطأ
- [ ] تطوير مكون CartIcon في الـ Header
- [ ] إضافة loading states لجميع العمليات
- [ ] تطبيق error handling شامل
- [ ] إضافة مكون CartDrawer (سلة منبثقة)

---

## المرحلة الثالثة: الدفع وحساب المستخدم (أسابيع 9-12)

### الأسبوع 9: مسار الدفع (Checkout)
**الأهداف:**
- تطوير عملية دفع سلسة ومؤمنة
- تصميم نموذج متعدد الخطوات

**المهام التفصيلية:**
- [ ] إنشاء صفحة الدفع المحمية (`/checkout`)
- [ ] تطوير نموذج متعدد الخطوات:
  - Step 1: معلومات الشحن
  - Step 2: طريقة الشحن
  - Step 3: طريقة الدفع
  - Step 4: مراجعة الطلب
- [ ] تطوير مكونات النموذج:
  - ShippingForm
  - ShippingMethodSelector
  - PaymentMethodSelector
  - OrderReview
- [ ] إضافة إمكانية استخدام العناوين المحفوظة
- [ ] تطوير StepIndicator لعرض التقدم
- [ ] إضافة validation شامل لجميع الحقول
- [ ] تطبيق حفظ البيانات محلياً (localStorage)

### الأسبوع 10: التكامل مع الدفع الإلكتروني
**الأهداف:**
- ربط بوابات الدفع المختلفة
- تطوير صفحات النتائج

**المهام التفصيلية:**
- [ ] تكامل مع Stripe React SDK
- [ ] تطوير مكونات الدفع:
  - CreditCardForm
  - PaymentMethodCard
  - PaymentProcessor
- [ ] إنشاء صفحة تأكيد الطلب (`/order/success/[orderId]`)
- [ ] إنشاء صفحة فشل الدفع (`/order/failed`)
- [ ] تطوير معالجة إعادة التوجيه من بوابات الدفع
- [ ] إضافة loading states أثناء المعالجة
- [ ] تطبيق error handling للدفع
- [ ] إضافة إشعارات البريد الإلكتروني (تكامل مع API)

### الأسبوع 11: صفحات حساب المستخدم
**الأهداف:**
- تطوير لوحة تحكم شاملة للمستخدم
- إدارة الطلبات والملف الشخصي

**المهام التفصيلية:**
- [ ] تطوير layout خاص بصفحات الحساب
- [ ] إنشاء صفحات الحساب:
  - `/account/dashboard` - لوحة التحكم الرئيسية
  - `/account/orders` - قائمة الطلبات
  - `/account/orders/[orderId]` - تفاصيل الطلب
  - `/account/addresses` - إدارة العناوين
  - `/account/profile` - تعديل الملف الشخصي
- [ ] تطوير مكونات الحساب:
  - AccountSidebar
  - OrderCard
  - OrderDetails
  - AddressCard
  - ProfileForm
- [ ] إضافة إمكانية تكرار الطلبات
- [ ] تطوير نظام إشعارات المستخدم
- [ ] إضافة تتبع الشحنات

### الأسبوع 12: التقييمات والمحتوى الثابت
**الأهداف:**
- تطوير نظام التقييمات والمراجعات
- إنشاء صفحات المحتوى الثابت

**المهام التفصيلية:**
- [ ] تطوير مكونات التقييمات:
  - ReviewCard
  - ReviewForm
  - StarRating
  - ReviewsList
- [ ] إضافة قسم التقييمات في صفحة المنتج
- [ ] تطوير نموذج إضافة تقييم جديد
- [ ] إنشاء صفحة ديناميكية للمحتوى (`/pages/[slug]`)
- [ ] تطوير صفحات ثابتة:
  - من نحن
  - سياسة الخصوصية
  - شروط الاستخدام
  - دليل القياسات
- [ ] استخدام getStaticProps و getStaticPaths للأداء
- [ ] إضافة نظام تصفية وترتيب التقييمات

---

## المرحلة الرابعة: تجربة المستخدم المتقدمة ولوحة التحكم (أسابيع 13-16)

### الأسبوع 13: الكوبونات والبحث
**الأهداف:**
- تفعيل نظام الكوبونات
- تطوير وظائف البحث المتقدمة

**المهام التفصيلية:**
- [ ] تفعيل حقل الكوبون في السلة:
  - CouponInput مكون
  - تطبيق الخصم
  - عرض الخصم في الملخص
- [ ] تطوير شريط البحث في الـ Header:
  - SearchInput مع autocomplete
  - اقتراحات البحث
  - تاريخ البحث
- [ ] إنشاء صفحة نتائج البحث (`/search`)
- [ ] تطوير مكونات البحث:
  - SearchResults
  - SearchFilters
  - NoResults
- [ ] إضافة البحث الصوتي (اختياري)
- [ ] تحسين SEO لصفحات البحث

### الأسبوع 14: الفلاتر المتقدمة ونقاط الولاء
**الأهداف:**
- تطوير نظام فلترة شامل
- تطبيق برنامج نقاط الولاء

**المهام التفصيلية:**
- [ ] تطوير Sidebar للفلاتر:
  - PriceRangeFilter
  - CategoryFilter
  - BrandFilter
  - RatingFilter
  - ColorFilter
  - SizeFilter
- [ ] ربط الفلاتر مع API parameters
- [ ] إضافة فلاتر متقدمة:
  - ترتيب النتائج
  - عدد النتائج في الصفحة
  - عرض شبكة أو قائمة
- [ ] تطوير صفحة نقاط الولاء (`/account/points`)
- [ ] تطوير مكونات نقاط الولاء:
  - PointsBalance
  - PointsHistory
  - RedeemPoints
- [ ] إضافة عرض النقاط في عملية الدفع

### الأسبوع 15: بدء لوحة تحكم المسؤول
**الأهداف:**
- تطوير الهيكل الأساسي للوحة التحكم
- بناء لوحة القيادة والمنتجات

**المهام التفصيلية:**
- [ ] إعداد هيكل لوحة التحكم (`/admin/*`)
- [ ] تطوير AdminLayout:
  - AdminSidebar
  - AdminHeader
  - AdminBreadcrumb
- [ ] تطوير لوحة القيادة (`/admin/dashboard`):
  - KPICards (المقاييس الرئيسية)
  - SalesChart
  - RecentActivities
  - PendingActions
- [ ] تطوير صفحة إدارة المنتجات (`/admin/products`):
  - ProductsTable
  - ProductFilters
  - BulkActions
- [ ] إضافة نظام صلاحيات أساسي
- [ ] تطوير مكونات الجداول القابلة لإعادة الاستخدام

### الأسبوع 16: استكمال لوحة التحكم (CRUD)
**الأهداف:**
- إكمال وظائف إدارة المنتجات والطلبات
- تطوير النماذج المتقدمة

**المهام التفصيلية:**
- [ ] تطوير نماذج المنتجات:
  - ProductForm (إضافة/تعديل)
  - ImageUploader
  - VariantsManager
  - SEOForm
- [ ] إنشاء صفحات:
  - `/admin/products/new`
  - `/admin/products/edit/[id]`
- [ ] تطوير صفحة إدارة الطلبات (`/admin/orders`):
  - OrdersTable
  - OrderStatusUpdater
  - OrderFilters
- [ ] تطوير صفحة تفاصيل الطلب للمسؤول
- [ ] إضافة إمكانية طباعة الفواتير
- [ ] تطوير نظام الإشعارات للمسؤولين

---

## المرحلة الخامسة: التحسين والاختبار والإطلاق (أسابيع 17-20)

### الأسبوع 17: استكمال لوحة التحكم
**الأهداف:**
- إكمال جميع وظائف لوحة التحكم
- كتابة الاختبارات الأساسية

**المهام التفصيلية:**
- [ ] تطوير الواجهات المتبقية:
  - إدارة العملاء (`/admin/customers`)
  - إدارة الكوبونات (`/admin/coupons`)
  - إدارة المحتوى (`/admin/content`)
  - الإعدادات (`/admin/settings`)
- [ ] تطوير مكونات متقدمة:
  - RichTextEditor للمحتوى
  - ImageGallery للمنتجات
  - DataExporter للتقارير
- [ ] كتابة اختبارات وحدة للمكونات المعقدة
- [ ] إضافة نظام النسخ الاحتياطي للبيانات
- [ ] تطوير نظام السجلات (Audit Logs)

### الأسبوع 18: تحسين الأداء وإمكانية الوصول
**الأهداف:**
- تحسين أداء التطبيق
- ضمان إمكانية الوصول

**المهام التفصيلية:**
- [ ] تحليل حجم الحزم باستخدام Bundle Analyzer
- [ ] تطبيق Code Splitting مع next/dynamic
- [ ] تحسين الصور باستخدام next/image:
  - تحديد الأولويات
  - تحسين الأحجام
  - Lazy loading
- [ ] تطبيق معايير WCAG:
  - السمات الدلالية
  - تباين الألوان
  - التنقل بلوحة المفاتيح
  - Screen reader support
- [ ] تحسين Core Web Vitals:
  - LCP (Largest Contentful Paint)
  - FID (First Input Delay)
  - CLS (Cumulative Layout Shift)
- [ ] إضافة Service Worker للتخزين المؤقت

### الأسبوع 19: الاختبار الشامل
**الأهداف:**
- تطوير اختبارات شاملة
- ضمان جودة التطبيق

**المهام التفصيلية:**
- [ ] كتابة اختبارات E2E باستخدام Cypress:
  - رحلة التسجيل والتفعيل
  - تصفح وإضافة المنتجات للسلة
  - تطبيق كوبون خصم
  - إتمام عملية الدفع
  - مراجعة الطلب في الحساب
- [ ] اختبارات التوافق عبر المتصفحات:
  - Chrome, Firefox, Safari, Edge
  - اختبار الأجهزة المحمولة
- [ ] اختبارات الأداء:
  - Load testing
  - Stress testing
  - Performance monitoring
- [ ] مراجعة الأمان:
  - XSS protection
  - CSRF protection
  - Input validation
- [ ] إصلاح جميع الأخطاء المكتشفة

### الأسبوع 20: الإطلاق والمراقبة
**الأهداف:**
- نشر النسخة النهائية
- إعداد المراقبة والتحليلات

**المهام التفصيلية:**
- [ ] إعداد بيئة الإنتاج على Vercel:
  - تكوين المتغيرات
  - ربط النطاق
  - إعداد SSL
- [ ] تكامل أدوات المراقبة:
  - Sentry لتتبع الأخطاء
  - Vercel Analytics للأداء
  - Google Analytics للسلوك
- [ ] تحسين SEO النهائي:
  - sitemap.xml
  - robots.txt
  - Meta tags
  - Schema markup
- [ ] إعداد النسخ الاحتياطية والاستعادة
- [ ] تدريب الفريق على استخدام لوحة التحكم
- [ ] إجراء فحص نهائي شامل
- [ ] الإطلاق الرسمي والإعلان

---

## 📊 المقاييس والمؤشرات

### مؤشرات الأداء
- **Core Web Vitals:** LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Page Load Speed:** < 3 ثواني للصفحة الرئيسية
- **Bundle Size:** < 500KB للحزمة الأولية
- **Lighthouse Score:** > 90 في جميع المقاييس

### مؤشرات تجربة المستخدم
- **Conversion Rate:** معدل تحويل الزوار إلى عملاء
- **Cart Abandonment:** معدل ترك السلة
- **User Engagement:** متوسط وقت البقاء في الموقع
- **Mobile Responsiveness:** 100% توافق مع الأجهزة المحمولة

### مؤشرات الجودة
- **Test Coverage:** > 80% تغطية الاختبارات
- **Bug Rate:** < 1% معدل الأخطاء في الإنتاج
- **Accessibility Score:** 100% توافق مع WCAG 2.1
- **Security Score:** A+ في اختبارات الأمان

---

## 🔧 الأدوات والمكتبات المطلوبة

### أدوات التطوير
- **Next.js 13+** - إطار العمل الأساسي
- **TypeScript** - لغة البرمجة المكتوبة
- **Tailwind CSS** - إطار عمل التصميم
- **Storybook** - تطوير المكونات المعزولة
- **React Hook Form** - إدارة النماذج
- **SWR/React Query** - جلب البيانات
- **Framer Motion** - الحركات والانتقالات

### أدوات الاختبار
- **Jest** - اختبارات الوحدة
- **React Testing Library** - اختبار المكونات
- **Cypress/Playwright** - الاختبارات الشاملة
- **Chromatic** - اختبار المكونات البصرية

### أدوات الإنتاج
- **Vercel** - النشر والاستضافة
- **Sentry** - تتبع الأخطاء
- **Google Analytics** - تحليل السلوك
- **Hotjar** - تحليل تجربة المستخدم

---

## 📈 خطة ما بعد الإطلاق

### التحسينات المستقبلية
- **PWA Support** - تحويل الموقع إلى تطبيق ويب تقدمي
- **AI Recommendations** - نظام توصيات ذكي
- **Voice Search** - البحث الصوتي
- **AR/VR Integration** - تجربة المنتجات افتراضياً
- **Advanced Analytics** - تحليلات متقدمة للسلوك

### الصيانة والدعم
- **Monthly Updates** - تحديثات شهرية للأمان والأداء
- **Feature Requests** - تطوير ميزات جديدة حسب الطلب
- **Performance Monitoring** - مراقبة مستمرة للأداء
- **User Feedback** - جمع وتحليل ملاحظات المستخدمين

---

## 🎯 الخلاصة

هذه الخطة الشاملة تضمن تطوير واجهة أمامية متقدمة ومتكاملة للمتجر الإلكتروني، مع التركيز على:

1. **تجربة المستخدم الاستثنائية** - تصميم بديهي وسلس
2. **الأداء العالي** - تحميل سريع واستجابة فورية
3. **القابلية للتوسع** - بنية قابلة للنمو والتطوير
4. **الأمان والموثوقية** - حماية شاملة للبيانات
5. **التوافق الشامل** - دعم جميع الأجهزة والمتصفحات

النتيجة النهائية ستكون متجر إلكتروني يضاهي أفضل المنصات العالمية في الجودة والأداء.

---

# 📄 تفصيل شامل لجميع صفحات الواجهة الأمامية

## 🏠 الصفحات العامة (Public Pages)

### 1. الصفحة الرئيسية (`/`)

**📋 الوصف:**
نقطة الدخول الرئيسية للمتجر، تهدف إلى جذب الزوار وتحويلهم إلى عملاء.

**🎯 الأهداف:**
- عرض هوية المتجر والعلامة التجارية
- إبراز المنتجات والعروض المميزة
- توجيه المستخدمين لاستكشاف المتجر
- تحسين معدل التحويل

**🧩 المكونات المطلوبة:**

#### Header Component
```typescript
interface HeaderProps {
  isLoggedIn: boolean;
  cartItemsCount: number;
  user?: User;
}
```
- **Logo:** شعار المتجر قابل للنقر للعودة للرئيسية
- **Navigation Menu:** قائمة الفئات الرئيسية
- **Search Bar:** شريط بحث مع autocomplete
- **User Menu:** تسجيل دخول/إنشاء حساب أو قائمة المستخدم
- **Cart Icon:** أيقونة السلة مع عداد المنتجات
- **Language Switcher:** تبديل اللغة (عربي/إنجليزي)
- **Currency Selector:** اختيار العملة

#### Hero Banner Section
```typescript
interface HeroBannerProps {
  banners: Banner[];
  autoPlay?: boolean;
  showDots?: boolean;
  showArrows?: boolean;
}
```
- **Carousel Container:** حاوي البنرات المتحركة
- **Banner Slides:** شرائح البنرات مع صور وعروض
- **Navigation Dots:** نقاط التنقل السفلية
- **Arrow Controls:** أسهم التنقل الجانبية
- **Call-to-Action Buttons:** أزرار الدعوة للعمل
- **Auto-play Timer:** مؤقت التشغيل التلقائي

#### Featured Categories Section
```typescript
interface CategoryCardProps {
  category: Category;
  imageUrl: string;
  productCount: number;
}
```
- **Section Title:** عنوان القسم "تسوق حسب الفئة"
- **Categories Grid:** شبكة عرض الفئات (3-4 أعمدة)
- **Category Card:** بطاقة الفئة مع صورة واسم
- **Hover Effects:** تأثيرات التفاعل عند التمرير
- **View All Button:** زر عرض جميع الفئات

#### Featured Products Section
```typescript
interface ProductCardProps {
  product: Product;
  showQuickView?: boolean;
  showWishlist?: boolean;
  showCompare?: boolean;
}
```
- **Section Title:** "المنتجات المميزة"
- **Products Carousel:** عرض دوار للمنتجات
- **Product Card:** بطاقة المنتج مع:
  - صورة المنتج مع تأثير hover
  - اسم المنتج
  - السعر (مع السعر المخفض إن وجد)
  - تقييم النجوم
  - أزرار سريعة (إضافة للسلة، المفضلة، مقارنة)
- **Navigation Arrows:** أسهم التنقل
- **View All Button:** زر عرض جميع المنتجات

#### New Arrivals Section
```typescript
interface NewArrivalsProps {
  products: Product[];
  limit: number;
}
```
- **Section Title:** "وصل حديثاً"
- **Products Grid:** شبكة المنتجات الجديدة
- **"New" Badge:** شارة "جديد" على المنتجات
- **Date Filter:** فلتر حسب تاريخ الإضافة
- **Load More Button:** زر تحميل المزيد

#### Newsletter Subscription
```typescript
interface NewsletterProps {
  onSubscribe: (email: string) => Promise<void>;
}
```
- **Section Background:** خلفية جذابة للقسم
- **Title & Description:** عنوان ووصف الاشتراك
- **Email Input:** حقل إدخال البريد الإلكتروني
- **Subscribe Button:** زر الاشتراك
- **Privacy Notice:** إشعار الخصوصية
- **Success/Error Messages:** رسائل النجاح والخطأ

#### Footer Component
```typescript
interface FooterProps {
  storeInfo: StoreInfo;
  socialLinks: SocialLink[];
  paymentMethods: PaymentMethod[];
}
```
- **Store Information:** معلومات المتجر والاتصال
- **Quick Links:** روابط سريعة (سياسة الخصوصية، الشروط، إلخ)
- **Customer Service:** خدمة العملاء وطرق التواصل
- **Social Media Links:** روابط وسائل التواصل الاجتماعي
- **Payment Methods:** طرق الدفع المقبولة
- **Copyright Notice:** حقوق الطبع والنشر

**📱 التصميم المتجاوب:**
- **Desktop:** تخطيط كامل مع جميع العناصر
- **Tablet:** تخطيط مكيف مع قوائم قابلة للطي
- **Mobile:** تخطيط عمودي مع قائمة همبرغر

**⚡ الأداء والتحسين:**
- **SSG + ISR:** توليد ثابت مع تحديث تدريجي
- **Image Optimization:** تحسين الصور مع next/image
- **Lazy Loading:** تحميل كسول للمحتوى
- **Caching Strategy:** استراتيجية تخزين مؤقت

**🔍 تحسين محركات البحث:**
- **Meta Tags:** علامات وصفية شاملة
- **Schema Markup:** ترميز البيانات المنظمة
- **Open Graph:** بيانات المشاركة الاجتماعية
- **Sitemap Integration:** تكامل مع خريطة الموقع

---

### 2. صفحة الفئة (`/category/[slug]`)

**📋 الوصف:**
صفحة عرض جميع المنتجات ضمن فئة معينة مع إمكانيات الفلترة والترتيب.

**🎯 الأهداف:**
- عرض منتجات الفئة بشكل منظم
- توفير أدوات فلترة وترتيب متقدمة
- تحسين تجربة التصفح والاكتشاف
- زيادة معدل التحويل

**🧩 المكونات المطلوبة:**

#### Category Header
```typescript
interface CategoryHeaderProps {
  category: Category;
  breadcrumbs: Breadcrumb[];
  totalProducts: number;
}
```
- **Breadcrumb Navigation:** مسار التنقل
- **Category Title:** اسم الفئة
- **Category Description:** وصف الفئة
- **Products Count:** عدد المنتجات الإجمالي
- **Category Image:** صورة الفئة (اختيارية)

#### Filters Sidebar
```typescript
interface FiltersSidebarProps {
  filters: FilterGroup[];
  activeFilters: ActiveFilter[];
  onFilterChange: (filters: ActiveFilter[]) => void;
}
```

##### Price Range Filter
- **Range Slider:** شريط تمرير النطاق السعري
- **Min/Max Inputs:** حقول الحد الأدنى والأقصى
- **Currency Display:** عرض العملة
- **Apply Button:** زر تطبيق الفلتر

##### Brand Filter
- **Brands List:** قائمة العلامات التجارية
- **Checkboxes:** مربعات اختيار متعددة
- **Search Brands:** بحث في العلامات التجارية
- **Show More/Less:** عرض المزيد/أقل

##### Size Filter
- **Size Options:** خيارات المقاسات
- **Visual Size Guide:** دليل المقاسات المرئي
- **Size Chart Link:** رابط جدول المقاسات

##### Color Filter
- **Color Swatches:** عينات الألوان
- **Color Names:** أسماء الألوان
- **Multi-select:** اختيار متعدد

##### Rating Filter
- **Star Ratings:** تقييمات النجوم
- **Rating Count:** عدد التقييمات لكل نجمة
- **Filter by Rating:** فلترة حسب التقييم

##### Availability Filter
- **In Stock:** متوفر في المخزون
- **Out of Stock:** غير متوفر
- **Pre-order:** طلب مسبق

#### Products Grid/List View
```typescript
interface ProductsViewProps {
  products: Product[];
  viewMode: 'grid' | 'list';
  loading: boolean;
  onViewModeChange: (mode: 'grid' | 'list') => void;
}
```

##### View Controls
- **Grid/List Toggle:** تبديل عرض الشبكة/القائمة
- **Products Per Page:** عدد المنتجات في الصفحة
- **Sort Dropdown:** قائمة الترتيب

##### Sort Options
- **Relevance:** الصلة
- **Price: Low to High:** السعر من الأقل للأعلى
- **Price: High to Low:** السعر من الأعلى للأقل
- **Newest First:** الأحدث أولاً
- **Best Selling:** الأكثر مبيعاً
- **Highest Rated:** الأعلى تقييماً

##### Product Grid
- **Responsive Grid:** شبكة متجاوبة (2-4 أعمدة)
- **Product Cards:** بطاقات المنتجات
- **Quick View Modal:** نافذة العرض السريع
- **Wishlist Integration:** تكامل قائمة الأمنيات

##### Product List View
- **Horizontal Layout:** تخطيط أفقي
- **Detailed Information:** معلومات مفصلة
- **Larger Images:** صور أكبر
- **Extended Description:** وصف موسع

#### Pagination Component
```typescript
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showPageNumbers?: boolean;
}
```
- **Page Numbers:** أرقام الصفحات
- **Previous/Next Buttons:** أزرار السابق/التالي
- **First/Last Buttons:** أزرار الأول/الأخير
- **Page Info:** معلومات الصفحة الحالية

#### Active Filters Display
```typescript
interface ActiveFiltersProps {
  filters: ActiveFilter[];
  onRemoveFilter: (filterId: string) => void;
  onClearAll: () => void;
}
```
- **Filter Tags:** علامات الفلاتر النشطة
- **Remove Buttons:** أزرار إزالة الفلاتر
- **Clear All Button:** زر مسح جميع الفلاتر
- **Results Count:** عدد النتائج

**📱 التصميم المتجاوب:**
- **Desktop:** sidebar + main content
- **Tablet:** collapsible filters
- **Mobile:** filter drawer/modal

**⚡ الأداء:**
- **SSR:** عرض من جانب الخادم
- **URL State Management:** إدارة حالة الرابط
- **Infinite Scroll:** تمرير لا نهائي (اختياري)
- **Debounced Search:** بحث مع تأخير

---

### 3. صفحة تفاصيل المنتج (`/product/[slug]`)

**📋 الوصف:**
صفحة شاملة لعرض جميع تفاصيل المنتج مع إمكانية إضافته للسلة.

**🎯 الأهداف:**
- عرض معلومات المنتج بشكل شامل وجذاب
- تسهيل عملية اتخاذ قرار الشراء
- توفير تجربة تفاعلية للمنتج
- زيادة معدل التحويل

**🧩 المكونات المطلوبة:**

#### Product Image Gallery
```typescript
interface ProductGalleryProps {
  images: ProductImage[];
  videos?: ProductVideo[];
  allowZoom?: boolean;
  showThumbnails?: boolean;
}
```

##### Main Image Display
- **Large Image Viewer:** عارض الصورة الرئيسية
- **Zoom Functionality:** إمكانية التكبير
- **Fullscreen Mode:** وضع ملء الشاشة
- **Image Loading States:** حالات تحميل الصور

##### Thumbnails Navigation
- **Thumbnail Strip:** شريط الصور المصغرة
- **Active Indicator:** مؤشر الصورة النشطة
- **Scroll Navigation:** تنقل التمرير
- **Touch/Swipe Support:** دعم اللمس والتمرير

##### Video Integration
- **Video Player:** مشغل الفيديو
- **Video Thumbnails:** صور مصغرة للفيديوهات
- **Play/Pause Controls:** أدوات التحكم
- **Video Quality Options:** خيارات جودة الفيديو

#### Product Information Panel
```typescript
interface ProductInfoProps {
  product: Product;
  selectedVariant?: ProductVariant;
  onVariantChange: (variant: ProductVariant) => void;
}
```

##### Basic Information
- **Product Title:** اسم المنتج
- **Brand Name:** اسم العلامة التجارية
- **SKU Display:** رقم المنتج
- **Availability Status:** حالة التوفر

##### Pricing Display
- **Current Price:** السعر الحالي
- **Original Price:** السعر الأصلي (مع خط)
- **Discount Percentage:** نسبة الخصم
- **Price Currency:** عملة السعر
- **Tax Information:** معلومات الضرائب

##### Rating and Reviews
- **Star Rating Display:** عرض تقييم النجوم
- **Average Rating:** متوسط التقييم
- **Reviews Count:** عدد المراجعات
- **Reviews Link:** رابط المراجعات

##### Product Description
- **Short Description:** وصف مختصر
- **Key Features:** الميزات الرئيسية
- **Specifications:** المواصفات التقنية
- **Care Instructions:** تعليمات العناية

#### Product Options Selector
```typescript
interface ProductOptionsProps {
  options: ProductOption[];
  selectedOptions: SelectedOption[];
  onOptionChange: (optionId: string, value: string) => void;
  availability: VariantAvailability[];
}
```

##### Color Selector
- **Color Swatches:** عينات الألوان
- **Color Names:** أسماء الألوان
- **Selected Indicator:** مؤشر اللون المحدد
- **Unavailable States:** حالات عدم التوفر

##### Size Selector
- **Size Buttons:** أزرار المقاسات
- **Size Guide Link:** رابط دليل المقاسات
- **Size Chart Modal:** نافذة جدول المقاسات
- **Recommended Size:** المقاس المقترح

##### Other Options
- **Material Selector:** محدد المواد
- **Style Variants:** متغيرات الأسلوب
- **Custom Options:** خيارات مخصصة

#### Quantity and Actions
```typescript
interface ProductActionsProps {
  product: Product;
  selectedVariant: ProductVariant;
  onAddToCart: (quantity: number) => void;
  onAddToWishlist: () => void;
}
```

##### Quantity Selector
- **Quantity Input:** حقل إدخال الكمية
- **Plus/Minus Buttons:** أزرار الزيادة والنقصان
- **Stock Limit:** حد المخزون
- **Quantity Validation:** التحقق من الكمية

##### Action Buttons
- **Add to Cart Button:** زر إضافة للسلة
- **Buy Now Button:** زر الشراء الفوري
- **Add to Wishlist:** إضافة للمفضلة
- **Share Product:** مشاركة المنتج
- **Compare Button:** زر المقارنة

##### Stock Information
- **Stock Status:** حالة المخزون
- **Stock Count:** عدد القطع المتوفرة
- **Low Stock Warning:** تحذير المخزون المنخفض
- **Restock Notification:** إشعار إعادة التوفر

#### Product Tabs Section
```typescript
interface ProductTabsProps {
  tabs: ProductTab[];
  defaultTab?: string;
}
```

##### Description Tab
- **Detailed Description:** وصف مفصل
- **Product Features:** ميزات المنتج
- **Usage Instructions:** تعليمات الاستخدام
- **Rich Text Content:** محتوى نصي غني

##### Specifications Tab
- **Technical Specs:** المواصفات التقنية
- **Dimensions:** الأبعاد
- **Weight:** الوزن
- **Materials:** المواد المستخدمة

##### Reviews Tab
- **Reviews List:** قائمة المراجعات
- **Review Form:** نموذج إضافة مراجعة
- **Review Filters:** فلاتر المراجعات
- **Helpful Votes:** تصويتات الإفادة

##### Shipping Tab
- **Shipping Options:** خيارات الشحن
- **Delivery Times:** أوقات التسليم
- **Shipping Costs:** تكاليف الشحن
- **Return Policy:** سياسة الإرجاع

#### Related Products Section
```typescript
interface RelatedProductsProps {
  products: Product[];
  title: string;
  type: 'related' | 'similar' | 'recommended';
}
```
- **Section Title:** عنوان القسم
- **Products Carousel:** عرض دوار للمنتجات
- **Product Cards:** بطاقات المنتجات المشابهة
- **View All Link:** رابط عرض الكل

#### Recently Viewed
```typescript
interface RecentlyViewedProps {
  products: Product[];
  maxItems: number;
}
```
- **Recently Viewed List:** قائمة المنتجات المشاهدة مؤخراً
- **Clear History:** مسح التاريخ
- **Local Storage Integration:** تكامل التخزين المحلي

**📱 التصميم المتجاوب:**
- **Desktop:** تخطيط جانبي (صور + معلومات)
- **Tablet:** تخطيط مكدس مع تحسينات
- **Mobile:** تخطيط عمودي كامل

**⚡ الأداء:**
- **SSR:** عرض من جانب الخادم للـ SEO
- **Image Optimization:** تحسين الصور
- **Lazy Loading:** تحميل كسول للمحتوى
- **Prefetching:** تحميل مسبق للمنتجات المشابهة

**🔍 تحسين محركات البحث:**
- **Product Schema:** ترميز بيانات المنتج
- **Rich Snippets:** مقتطفات غنية
- **Meta Tags:** علامات وصفية ديناميكية
- **Canonical URLs:** روابط أساسية

---

### 4. صفحة سلة التسوق (`/cart`)

**📋 الوصف:**
صفحة عرض وإدارة المنتجات المضافة للسلة قبل الانتقال للدفع.

**🎯 الأهداف:**
- عرض محتويات السلة بوضوح
- تسهيل تعديل الكميات والمنتجات
- عرض ملخص التكاليف الشامل
- توجيه المستخدم لإتمام الطلب

**🧩 المكونات المطلوبة:**

#### Cart Header
```typescript
interface CartHeaderProps {
  itemsCount: number;
  isLoading: boolean;
}
```
- **Page Title:** عنوان الصفحة "سلة التسوق"
- **Items Count:** عدد المنتجات في السلة
- **Breadcrumb:** مسار التنقل
- **Continue Shopping Link:** رابط متابعة التسوق

#### Cart Items List
```typescript
interface CartItemsProps {
  items: CartItem[];
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  isUpdating: boolean;
}
```

##### Cart Item Component
```typescript
interface CartItemProps {
  item: CartItem;
  onQuantityChange: (quantity: number) => void;
  onRemove: () => void;
  isUpdating: boolean;
}
```
- **Product Image:** صورة المنتج
- **Product Name:** اسم المنتج
- **Product Variant:** متغير المنتج (لون، مقاس)
- **Unit Price:** سعر الوحدة
- **Quantity Selector:** محدد الكمية
- **Subtotal:** المجموع الفرعي للمنتج
- **Remove Button:** زر الحذف
- **Stock Status:** حالة المخزون
- **Save for Later:** حفظ لوقت لاحق

##### Quantity Controls
- **Decrease Button:** زر تقليل الكمية
- **Quantity Input:** حقل إدخال الكمية
- **Increase Button:** زر زيادة الكمية
- **Stock Limit:** حد المخزون المتاح
- **Update Loading:** حالة تحديث الكمية

##### Item Actions
- **Remove Confirmation:** تأكيد الحذف
- **Move to Wishlist:** نقل للمفضلة
- **Product Link:** رابط صفحة المنتج
- **Availability Check:** فحص التوفر

#### Order Summary
```typescript
interface OrderSummaryProps {
  subtotal: number;
  shipping: number;
  tax: number;
  discount: number;
  total: number;
  currency: string;
}
```

##### Cost Breakdown
- **Subtotal:** المجموع الفرعي
- **Shipping Cost:** تكلفة الشحن
- **Tax Amount:** مبلغ الضريبة
- **Discount Applied:** الخصم المطبق
- **Total Amount:** المبلغ الإجمالي

##### Shipping Calculator
- **Shipping Address:** عنوان الشحن
- **Shipping Methods:** طرق الشحن المتاحة
- **Delivery Time:** وقت التسليم المتوقع
- **Shipping Cost:** تكلفة الشحن

#### Coupon Section
```typescript
interface CouponSectionProps {
  appliedCoupon?: Coupon;
  onApplyCoupon: (code: string) => void;
  onRemoveCoupon: () => void;
  isApplying: boolean;
}
```
- **Coupon Input:** حقل إدخال كود الكوبون
- **Apply Button:** زر تطبيق الكوبون
- **Applied Coupon Display:** عرض الكوبون المطبق
- **Remove Coupon:** إزالة الكوبون
- **Coupon Validation:** التحقق من صحة الكوبون
- **Error Messages:** رسائل الخطأ

#### Checkout Actions
```typescript
interface CheckoutActionsProps {
  isCartEmpty: boolean;
  isProcessing: boolean;
  onProceedToCheckout: () => void;
}
```
- **Proceed to Checkout Button:** زر الانتقال للدفع
- **Secure Checkout Badge:** شارة الدفع الآمن
- **Payment Methods Preview:** معاينة طرق الدفع
- **SSL Certificate Info:** معلومات شهادة الأمان

#### Empty Cart State
```typescript
interface EmptyCartProps {
  onContinueShopping: () => void;
  recommendedProducts?: Product[];
}
```
- **Empty Cart Icon:** أيقونة السلة الفارغة
- **Empty Message:** رسالة السلة الفارغة
- **Continue Shopping Button:** زر متابعة التسوق
- **Recommended Products:** منتجات مقترحة
- **Recently Viewed:** منتجات مشاهدة مؤخراً

#### Saved for Later
```typescript
interface SavedItemsProps {
  savedItems: SavedItem[];
  onMoveToCart: (itemId: string) => void;
  onRemoveSaved: (itemId: string) => void;
}
```
- **Saved Items List:** قائمة المنتجات المحفوظة
- **Move to Cart:** نقل للسلة
- **Remove from Saved:** حذف من المحفوظات
- **Saved Items Count:** عدد المنتجات المحفوظة

**📱 التصميم المتجاوب:**
- **Desktop:** تخطيط جانبي (قائمة + ملخص)
- **Tablet:** تخطيط مكدس مع تحسينات
- **Mobile:** تخطيط عمودي مع ملخص ثابت

**⚡ الأداء:**
- **Real-time Updates:** تحديثات فورية
- **Optimistic Updates:** تحديثات متفائلة
- **Error Recovery:** استرداد الأخطاء
- **Local Storage Backup:** نسخ احتياطي محلي

---

### 5. صفحة الدفع (`/checkout`)

**📋 الوصف:**
صفحة إتمام عملية الشراء وجمع معلومات الدفع والشحن.

**🎯 الأهداف:**
- تسهيل عملية إتمام الطلب
- جمع معلومات الشحن والدفع بأمان
- توفير تجربة دفع سلسة ومؤمنة
- تقليل معدل ترك السلة

**🧩 المكونات المطلوبة:**

#### Checkout Header
```typescript
interface CheckoutHeaderProps {
  currentStep: number;
  totalSteps: number;
  steps: CheckoutStep[];
}
```
- **Progress Indicator:** مؤشر التقدم
- **Step Labels:** تسميات الخطوات
- **Security Badge:** شارة الأمان
- **SSL Certificate:** شهادة الأمان

#### Multi-Step Form
```typescript
interface CheckoutFormProps {
  onStepChange: (step: number) => void;
  onSubmit: (data: CheckoutData) => void;
  initialData?: Partial<CheckoutData>;
}
```

##### Step 1: Shipping Information
```typescript
interface ShippingFormProps {
  savedAddresses: Address[];
  onAddressSelect: (address: Address) => void;
  onNewAddress: (address: Address) => void;
}
```

###### Guest/Login Options
- **Guest Checkout:** خيار الدفع كضيف
- **Login Form:** نموذج تسجيل الدخول
- **Register Option:** خيار إنشاء حساب
- **Social Login:** تسجيل دخول اجتماعي

###### Address Form
- **First Name:** الاسم الأول
- **Last Name:** الاسم الأخير
- **Email Address:** البريد الإلكتروني
- **Phone Number:** رقم الهاتف
- **Address Line 1:** العنوان الأول
- **Address Line 2:** العنوان الثاني (اختياري)
- **City:** المدينة
- **State/Province:** الولاية/المحافظة
- **Postal Code:** الرمز البريدي
- **Country:** البلد

###### Saved Addresses
- **Address List:** قائمة العناوين المحفوظة
- **Select Address:** اختيار عنوان
- **Edit Address:** تعديل العنوان
- **Add New Address:** إضافة عنوان جديد
- **Set as Default:** تعيين كافتراضي

##### Step 2: Shipping Method
```typescript
interface ShippingMethodProps {
  methods: ShippingMethod[];
  selectedMethod: string;
  onMethodSelect: (methodId: string) => void;
}
```
- **Shipping Options:** خيارات الشحن المتاحة
- **Delivery Time:** وقت التسليم المتوقع
- **Shipping Cost:** تكلفة الشحن
- **Method Description:** وصف طريقة الشحن
- **Express Options:** خيارات الشحن السريع

##### Step 3: Payment Method
```typescript
interface PaymentMethodProps {
  methods: PaymentMethod[];
  selectedMethod: string;
  onMethodSelect: (methodId: string) => void;
}
```

###### Payment Options
- **Credit/Debit Card:** بطاقة ائتمان/خصم
- **Digital Wallets:** المحافظ الرقمية (Apple Pay, Google Pay)
- **Bank Transfer:** تحويل بنكي
- **Cash on Delivery:** الدفع عند الاستلام
- **Buy Now Pay Later:** اشتر الآن وادفع لاحقاً

###### Credit Card Form
- **Card Number:** رقم البطاقة
- **Cardholder Name:** اسم حامل البطاقة
- **Expiry Date:** تاريخ الانتهاء
- **CVV:** رمز الأمان
- **Billing Address:** عنوان الفوترة
- **Save Card Option:** خيار حفظ البطاقة

###### Security Features
- **SSL Encryption:** تشفير SSL
- **PCI Compliance:** امتثال PCI
- **3D Secure:** الأمان ثلاثي الأبعاد
- **Fraud Protection:** حماية من الاحتيال

##### Step 4: Order Review
```typescript
interface OrderReviewProps {
  orderData: OrderData;
  onEdit: (section: string) => void;
  onConfirm: () => void;
}
```

###### Order Summary
- **Items List:** قائمة المنتجات
- **Quantities:** الكميات
- **Prices:** الأسعار
- **Subtotal:** المجموع الفرعي
- **Shipping:** الشحن
- **Tax:** الضريبة
- **Total:** الإجمالي

###### Shipping Details
- **Delivery Address:** عنوان التسليم
- **Shipping Method:** طريقة الشحن
- **Estimated Delivery:** التسليم المتوقع
- **Tracking Info:** معلومات التتبع

###### Payment Details
- **Payment Method:** طريقة الدفع
- **Billing Address:** عنوان الفوترة
- **Card Info:** معلومات البطاقة (مخفية)

#### Order Confirmation
```typescript
interface OrderConfirmationProps {
  order: Order;
  paymentStatus: PaymentStatus;
  onContinueShopping: () => void;
}
```
- **Order Number:** رقم الطلب
- **Confirmation Message:** رسالة التأكيد
- **Order Details:** تفاصيل الطلب
- **Payment Receipt:** إيصال الدفع
- **Tracking Information:** معلومات التتبع
- **Email Confirmation:** تأكيد البريد الإلكتروني

#### Error Handling
```typescript
interface CheckoutErrorProps {
  error: CheckoutError;
  onRetry: () => void;
  onGoBack: () => void;
}
```
- **Error Messages:** رسائل الخطأ
- **Retry Options:** خيارات إعادة المحاولة
- **Support Contact:** تواصل مع الدعم
- **Alternative Methods:** طرق بديلة

**📱 التصميم المتجاوب:**
- **Desktop:** نموذج واسع مع خطوات جانبية
- **Tablet:** نموذج مكدس مع تنقل محسن
- **Mobile:** خطوات منفصلة مع تنقل سهل

**🔒 الأمان:**
- **HTTPS Only:** HTTPS فقط
- **Input Validation:** التحقق من المدخلات
- **CSRF Protection:** حماية CSRF
- **Rate Limiting:** تحديد المعدل

---

## 🔐 صفحات المصادقة (Authentication Pages)

### 6. صفحة تسجيل الدخول (`/login`)

**📋 الوصف:**
صفحة تسجيل دخول المستخدمين المسجلين للوصول لحساباتهم.

**🎯 الأهداف:**
- تسهيل عملية تسجيل الدخول
- توفير خيارات متعددة للدخول
- ضمان أمان عملية المصادقة
- تحسين تجربة المستخدم

**🧩 المكونات المطلوبة:**

#### Login Form
```typescript
interface LoginFormProps {
  onSubmit: (credentials: LoginCredentials) => void;
  isLoading: boolean;
  error?: string;
}
```

##### Form Fields
- **Email/Username Input:** حقل البريد الإلكتروني أو اسم المستخدم
- **Password Input:** حقل كلمة المرور
- **Show/Hide Password:** إظهار/إخفاء كلمة المرور
- **Remember Me Checkbox:** مربع "تذكرني"
- **Form Validation:** التحقق من صحة النموذج

##### Login Actions
- **Login Button:** زر تسجيل الدخول
- **Loading State:** حالة التحميل
- **Error Messages:** رسائل الخطأ
- **Success Redirect:** إعادة التوجيه عند النجاح

#### Social Login
```typescript
interface SocialLoginProps {
  providers: SocialProvider[];
  onSocialLogin: (provider: string) => void;
}
```
- **Google Login:** تسجيل دخول بجوجل
- **Facebook Login:** تسجيل دخول بفيسبوك
- **Apple Login:** تسجيل دخول بآبل
- **Twitter Login:** تسجيل دخول بتويتر

#### Additional Options
- **Forgot Password Link:** رابط نسيان كلمة المرور
- **Register Link:** رابط إنشاء حساب جديد
- **Guest Checkout:** خيار الدفع كضيف
- **Back to Store:** العودة للمتجر

#### Security Features
- **CAPTCHA:** كابتشا للحماية
- **Rate Limiting:** تحديد عدد المحاولات
- **Account Lockout:** قفل الحساب
- **Two-Factor Auth:** المصادقة الثنائية

---

### 7. صفحة التسجيل (`/register`)

**📋 الوصف:**
صفحة إنشاء حساب جديد للمستخدمين الجدد.

**🎯 الأهداف:**
- تسهيل عملية إنشاء الحساب
- جمع المعلومات الأساسية
- التحقق من صحة البيانات
- تفعيل الحساب بأمان

**🧩 المكونات المطلوبة:**

#### Registration Form
```typescript
interface RegisterFormProps {
  onSubmit: (userData: RegisterData) => void;
  isLoading: boolean;
  errors?: ValidationErrors;
}
```

##### Personal Information
- **First Name:** الاسم الأول
- **Last Name:** الاسم الأخير
- **Email Address:** البريد الإلكتروني
- **Phone Number:** رقم الهاتف
- **Date of Birth:** تاريخ الميلاد (اختياري)
- **Gender:** الجنس (اختياري)

##### Account Security
- **Password Input:** كلمة المرور
- **Confirm Password:** تأكيد كلمة المرور
- **Password Strength:** قوة كلمة المرور
- **Security Questions:** أسئلة الأمان (اختياري)

##### Preferences
- **Newsletter Subscription:** الاشتراك في النشرة
- **SMS Notifications:** إشعارات الرسائل
- **Marketing Emails:** رسائل تسويقية
- **Language Preference:** تفضيل اللغة

##### Terms and Privacy
- **Terms Checkbox:** موافقة على الشروط
- **Privacy Policy:** سياسة الخصوصية
- **Age Verification:** التحقق من العمر
- **Marketing Consent:** موافقة التسويق

#### Email Verification
```typescript
interface EmailVerificationProps {
  email: string;
  onResendEmail: () => void;
  onVerify: (token: string) => void;
}
```
- **Verification Message:** رسالة التحقق
- **Email Sent Notice:** إشعار إرسال البريد
- **Resend Email:** إعادة إرسال البريد
- **Verification Form:** نموذج التحقق

---

## 👤 صفحات حساب المستخدم (User Account Pages)

### 8. لوحة تحكم المستخدم (`/account/dashboard`)

**📋 الوصف:**
الصفحة الرئيسية لحساب المستخدم مع نظرة عامة على النشاط.

**🎯 الأهداف:**
- عرض ملخص نشاط الحساب
- توفير تنقل سهل لأقسام الحساب
- عرض المعلومات المهمة
- تحسين تجربة إدارة الحساب

**🧩 المكونات المطلوبة:**

#### Account Layout
```typescript
interface AccountLayoutProps {
  user: User;
  currentPage: string;
  children: React.ReactNode;
}
```

##### Account Sidebar
- **User Avatar:** صورة المستخدم
- **User Name:** اسم المستخدم
- **Account Menu:** قائمة أقسام الحساب
- **Logout Button:** زر تسجيل الخروج

##### Navigation Menu
- **Dashboard:** لوحة التحكم
- **Orders:** الطلبات
- **Addresses:** العناوين
- **Profile:** الملف الشخصي
- **Wishlist:** المفضلة
- **Points:** نقاط الولاء
- **Settings:** الإعدادات

#### Dashboard Overview
```typescript
interface DashboardOverviewProps {
  user: User;
  stats: UserStats;
  recentOrders: Order[];
}
```

##### Welcome Section
- **Welcome Message:** رسالة ترحيب شخصية
- **Account Status:** حالة الحساب
- **Member Since:** عضو منذ
- **Profile Completion:** اكتمال الملف الشخصي

##### Quick Stats
- **Total Orders:** إجمالي الطلبات
- **Total Spent:** إجمالي المبلغ المنفق
- **Loyalty Points:** نقاط الولاء
- **Wishlist Items:** عناصر المفضلة

##### Recent Activity
- **Recent Orders:** الطلبات الأخيرة
- **Order Status:** حالة الطلبات
- **Tracking Info:** معلومات التتبع
- **Quick Actions:** إجراءات سريعة

#### Notifications Panel
```typescript
interface NotificationsPanelProps {
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onClearAll: () => void;
}
```
- **New Notifications:** الإشعارات الجديدة
- **Order Updates:** تحديثات الطلبات
- **Promotional Offers:** العروض الترويجية
- **System Messages:** رسائل النظام

---

### 9. صفحة الطلبات (`/account/orders`)

**📋 الوصف:**
صفحة عرض جميع طلبات المستخدم مع إمكانية التتبع والإدارة.

**🧩 المكونات المطلوبة:**

#### Orders List
```typescript
interface OrdersListProps {
  orders: Order[];
  filters: OrderFilters;
  onFilterChange: (filters: OrderFilters) => void;
}
```

##### Orders Filter
- **Status Filter:** فلتر حالة الطلب
- **Date Range:** نطاق التاريخ
- **Order Amount:** مبلغ الطلب
- **Search Orders:** البحث في الطلبات

##### Order Card
```typescript
interface OrderCardProps {
  order: Order;
  onViewDetails: (orderId: string) => void;
  onTrackOrder: (orderId: string) => void;
}
```
- **Order Number:** رقم الطلب
- **Order Date:** تاريخ الطلب
- **Order Status:** حالة الطلب
- **Total Amount:** المبلغ الإجمالي
- **Items Count:** عدد المنتجات
- **Action Buttons:** أزرار الإجراءات

---

### 10. تفاصيل الطلب (`/account/orders/[orderId]`)

**📋 الوصف:**
صفحة عرض تفاصيل طلب محدد مع جميع المعلومات.

**🧩 المكونات المطلوبة:**

#### Order Header
```typescript
interface OrderHeaderProps {
  order: Order;
  onDownloadInvoice: () => void;
  onTrackOrder: () => void;
}
```
- **Order Number:** رقم الطلب
- **Order Status:** حالة الطلب
- **Order Date:** تاريخ الطلب
- **Download Invoice:** تحميل الفاتورة

#### Order Items
```typescript
interface OrderItemsProps {
  items: OrderItem[];
  onReorder: (items: OrderItem[]) => void;
  onReviewProduct: (productId: string) => void;
}
```
- **Items List:** قائمة المنتجات
- **Product Details:** تفاصيل المنتج
- **Quantities:** الكميات
- **Prices:** الأسعار
- **Review Options:** خيارات المراجعة

#### Shipping Information
- **Delivery Address:** عنوان التسليم
- **Shipping Method:** طريقة الشحن
- **Tracking Number:** رقم التتبع
- **Delivery Status:** حالة التسليم

#### Payment Information
- **Payment Method:** طريقة الدفع
- **Payment Status:** حالة الدفع
- **Billing Address:** عنوان الفوترة
- **Transaction ID:** رقم المعاملة

---

### 11. إدارة العناوين (`/account/addresses`)

**📋 الوصف:**
صفحة إدارة عناوين الشحن والفوترة للمستخدم.

**🧩 المكونات المطلوبة:**

#### Addresses List
```typescript
interface AddressesListProps {
  addresses: Address[];
  onEdit: (address: Address) => void;
  onDelete: (addressId: string) => void;
  onSetDefault: (addressId: string) => void;
}
```

##### Address Card
```typescript
interface AddressCardProps {
  address: Address;
  isDefault: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onSetDefault: () => void;
}
```
- **Address Type:** نوع العنوان (منزل، عمل، إلخ)
- **Full Address:** العنوان الكامل
- **Contact Info:** معلومات الاتصال
- **Default Badge:** شارة العنوان الافتراضي
- **Action Buttons:** أزرار الإجراءات

#### Add/Edit Address Form
```typescript
interface AddressFormProps {
  address?: Address;
  onSubmit: (address: Address) => void;
  onCancel: () => void;
}
```
- **Address Type Selector:** محدد نوع العنوان
- **Contact Information:** معلومات الاتصال
- **Address Fields:** حقول العنوان
- **Set as Default:** تعيين كافتراضي
- **Save Button:** زر الحفظ

---

### 12. الملف الشخصي (`/account/profile`)

**📋 الوصف:**
صفحة تعديل المعلومات الشخصية وإعدادات الحساب.

**🧩 المكونات المطلوبة:**

#### Profile Form
```typescript
interface ProfileFormProps {
  user: User;
  onUpdate: (userData: UserData) => void;
  isUpdating: boolean;
}
```

##### Personal Information
- **Profile Picture:** صورة الملف الشخصي
- **First Name:** الاسم الأول
- **Last Name:** الاسم الأخير
- **Email Address:** البريد الإلكتروني
- **Phone Number:** رقم الهاتف
- **Date of Birth:** تاريخ الميلاد
- **Gender:** الجنس

##### Account Settings
- **Language Preference:** تفضيل اللغة
- **Currency Preference:** تفضيل العملة
- **Timezone:** المنطقة الزمنية
- **Newsletter Subscription:** الاشتراك في النشرة

#### Change Password
```typescript
interface ChangePasswordProps {
  onChangePassword: (passwords: PasswordData) => void;
  isChanging: boolean;
}
```
- **Current Password:** كلمة المرور الحالية
- **New Password:** كلمة المرور الجديدة
- **Confirm Password:** تأكيد كلمة المرور
- **Password Strength:** قوة كلمة المرور

#### Privacy Settings
- **Data Sharing:** مشاركة البيانات
- **Marketing Emails:** رسائل تسويقية
- **SMS Notifications:** إشعارات الرسائل
- **Account Visibility:** رؤية الحساب

---

### 13. نقاط الولاء (`/account/points`)

**📋 الوصف:**
صفحة عرض وإدارة نقاط الولاء والمكافآت.

**🧩 المكونات المطلوبة:**

#### Points Overview
```typescript
interface PointsOverviewProps {
  currentBalance: number;
  totalEarned: number;
  totalRedeemed: number;
  expiringPoints: number;
}
```
- **Current Balance:** الرصيد الحالي
- **Points Value:** قيمة النقاط بالعملة
- **Expiring Soon:** نقاط تنتهي قريباً
- **Earning Rate:** معدل الكسب

#### Points History
```typescript
interface PointsHistoryProps {
  transactions: PointsTransaction[];
  filters: PointsFilters;
  onFilterChange: (filters: PointsFilters) => void;
}
```
- **Transaction List:** قائمة المعاملات
- **Earned Points:** النقاط المكتسبة
- **Redeemed Points:** النقاط المستخدمة
- **Transaction Date:** تاريخ المعاملة
- **Description:** وصف المعاملة

#### Rewards Catalog
```typescript
interface RewardsCatalogProps {
  rewards: Reward[];
  userPoints: number;
  onRedeem: (rewardId: string) => void;
}
```
- **Available Rewards:** المكافآت المتاحة
- **Points Required:** النقاط المطلوبة
- **Reward Description:** وصف المكافأة
- **Redeem Button:** زر الاستبدال

---

## 🛠️ لوحة تحكم المسؤول (Admin Panel Pages)

### 14. لوحة القيادة (`/admin/dashboard`)

**📋 الوصف:**
الصفحة الرئيسية للوحة تحكم المسؤول مع الإحصائيات والمقاييس.

**🧩 المكونات المطلوبة:**

#### Admin Layout
```typescript
interface AdminLayoutProps {
  user: AdminUser;
  currentPage: string;
  children: React.ReactNode;
}
```

##### Admin Sidebar
- **Logo:** شعار لوحة التحكم
- **Navigation Menu:** قائمة التنقل الرئيسية
- **User Info:** معلومات المسؤول
- **Collapse Toggle:** زر طي القائمة

##### Admin Header
- **Page Title:** عنوان الصفحة
- **Breadcrumb:** مسار التنقل
- **Notifications:** الإشعارات
- **User Menu:** قائمة المستخدم

#### Key Metrics Cards
```typescript
interface MetricsCardsProps {
  metrics: DashboardMetrics;
  period: TimePeriod;
  onPeriodChange: (period: TimePeriod) => void;
}
```
- **Total Sales:** إجمالي المبيعات
- **Orders Count:** عدد الطلبات
- **New Customers:** العملاء الجدد
- **Average Order Value:** متوسط قيمة الطلب
- **Conversion Rate:** معدل التحويل
- **Revenue Growth:** نمو الإيرادات

#### Sales Chart
```typescript
interface SalesChartProps {
  data: SalesData[];
  chartType: 'line' | 'bar' | 'area';
  period: TimePeriod;
}
```
- **Chart Container:** حاوي الرسم البياني
- **Chart Controls:** أدوات التحكم
- **Data Visualization:** تصور البيانات
- **Export Options:** خيارات التصدير

#### Recent Activities
```typescript
interface RecentActivitiesProps {
  activities: Activity[];
  limit: number;
  onViewAll: () => void;
}
```
- **Activity Feed:** تغذية النشاط
- **Activity Types:** أنواع النشاط
- **Timestamps:** الطوابع الزمنية
- **User Actions:** إجراءات المستخدمين

#### Pending Actions
```typescript
interface PendingActionsProps {
  pendingOrders: number;
  pendingReviews: number;
  lowStockProducts: number;
  onActionClick: (action: string) => void;
}
```
- **Pending Orders:** الطلبات المعلقة
- **Pending Reviews:** المراجعات المعلقة
- **Low Stock Alerts:** تنبيهات المخزون المنخفض
- **System Notifications:** إشعارات النظام

---

### 15. إدارة المنتجات (`/admin/products`)

**📋 الوصف:**
صفحة إدارة كتالوج المنتجات مع إمكانيات البحث والفلترة.

**🧩 المكونات المطلوبة:**

#### Products Header
```typescript
interface ProductsHeaderProps {
  totalProducts: number;
  onAddProduct: () => void;
  onBulkAction: (action: string, productIds: string[]) => void;
}
```
- **Page Title:** عنوان الصفحة
- **Add Product Button:** زر إضافة منتج
- **Bulk Actions:** إجراءات مجمعة
- **Export Options:** خيارات التصدير

#### Products Filters
```typescript
interface ProductsFiltersProps {
  filters: ProductFilters;
  onFilterChange: (filters: ProductFilters) => void;
  onClearFilters: () => void;
}
```
- **Search Input:** حقل البحث
- **Category Filter:** فلتر الفئة
- **Status Filter:** فلتر الحالة
- **Stock Filter:** فلتر المخزون
- **Price Range:** نطاق السعر
- **Date Range:** نطاق التاريخ

#### Products Table
```typescript
interface ProductsTableProps {
  products: Product[];
  selectedProducts: string[];
  onSelectProduct: (productId: string) => void;
  onSelectAll: () => void;
  onEdit: (productId: string) => void;
  onDelete: (productId: string) => void;
}
```

##### Table Columns
- **Checkbox:** مربع الاختيار
- **Product Image:** صورة المنتج
- **Product Name:** اسم المنتج
- **SKU:** رقم المنتج
- **Category:** الفئة
- **Price:** السعر
- **Stock:** المخزون
- **Status:** الحالة
- **Actions:** الإجراءات

##### Table Features
- **Sorting:** الترتيب
- **Pagination:** التصفح
- **Row Selection:** اختيار الصفوف
- **Quick Edit:** تعديل سريع

---

### 16. إضافة/تعديل المنتج (`/admin/products/new`, `/admin/products/edit/[id]`)

**📋 الوصف:**
صفحة إضافة منتج جديد أو تعديل منتج موجود.

**🧩 المكونات المطلوبة:**

#### Product Form
```typescript
interface ProductFormProps {
  product?: Product;
  onSubmit: (productData: ProductData) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}
```

##### Basic Information
- **Product Name:** اسم المنتج
- **Product Description:** وصف المنتج
- **Short Description:** وصف مختصر
- **SKU:** رقم المنتج
- **Barcode:** الباركود
- **Brand:** العلامة التجارية

##### Pricing
- **Regular Price:** السعر العادي
- **Sale Price:** سعر التخفيض
- **Cost Price:** سعر التكلفة
- **Tax Class:** فئة الضريبة
- **Currency:** العملة

##### Inventory
- **Track Quantity:** تتبع الكمية
- **Stock Quantity:** كمية المخزون
- **Low Stock Threshold:** حد المخزون المنخفض
- **Stock Status:** حالة المخزون
- **Backorders:** الطلبات المؤجلة

##### Categories and Tags
- **Product Categories:** فئات المنتج
- **Product Tags:** علامات المنتج
- **Featured Product:** منتج مميز
- **Visibility:** الرؤية

#### Product Images
```typescript
interface ProductImagesProps {
  images: ProductImage[];
  onAddImage: (image: File) => void;
  onRemoveImage: (imageId: string) => void;
  onSetFeatured: (imageId: string) => void;
}
```
- **Image Upload:** رفع الصور
- **Image Gallery:** معرض الصور
- **Featured Image:** الصورة المميزة
- **Image Order:** ترتيب الصور
- **Alt Text:** النص البديل

#### Product Variants
```typescript
interface ProductVariantsProps {
  variants: ProductVariant[];
  onAddVariant: (variant: ProductVariant) => void;
  onEditVariant: (variantId: string, variant: ProductVariant) => void;
  onRemoveVariant: (variantId: string) => void;
}
```
- **Variant Attributes:** خصائص المتغيرات
- **Variant Options:** خيارات المتغيرات
- **Variant Pricing:** تسعير المتغيرات
- **Variant Stock:** مخزون المتغيرات
- **Variant Images:** صور المتغيرات

#### SEO Settings
```typescript
interface SEOSettingsProps {
  seoData: SEOData;
  onSEOChange: (seoData: SEOData) => void;
}
```
- **Meta Title:** عنوان الميتا
- **Meta Description:** وصف الميتا
- **URL Slug:** رابط الصفحة
- **Focus Keyword:** الكلمة المفتاحية
- **Schema Markup:** ترميز البيانات

---

### 17. إدارة الطلبات (`/admin/orders`)

**📋 الوصف:**
صفحة إدارة ومتابعة جميع طلبات العملاء.

**🧩 المكونات المطلوبة:**

#### Orders Header
```typescript
interface OrdersHeaderProps {
  totalOrders: number;
  pendingOrders: number;
  onExportOrders: () => void;
}
```
- **Orders Statistics:** إحصائيات الطلبات
- **Quick Filters:** فلاتر سريعة
- **Export Button:** زر التصدير
- **Refresh Button:** زر التحديث

#### Orders Filters
```typescript
interface OrdersFiltersProps {
  filters: OrderFilters;
  onFilterChange: (filters: OrderFilters) => void;
}
```
- **Order Status:** حالة الطلب
- **Date Range:** نطاق التاريخ
- **Customer Search:** البحث عن العميل
- **Payment Status:** حالة الدفع
- **Shipping Method:** طريقة الشحن
- **Order Amount:** مبلغ الطلب

#### Orders Table
```typescript
interface OrdersTableProps {
  orders: Order[];
  onViewOrder: (orderId: string) => void;
  onUpdateStatus: (orderId: string, status: OrderStatus) => void;
  onPrintInvoice: (orderId: string) => void;
}
```

##### Table Columns
- **Order Number:** رقم الطلب
- **Customer:** العميل
- **Date:** التاريخ
- **Status:** الحالة
- **Total:** الإجمالي
- **Payment:** الدفع
- **Shipping:** الشحن
- **Actions:** الإجراءات

##### Quick Actions
- **Status Update:** تحديث الحالة
- **Print Invoice:** طباعة الفاتورة
- **Send Email:** إرسال بريد
- **View Details:** عرض التفاصيل

---

### 18. تفاصيل الطلب للمسؤول (`/admin/orders/[orderId]`)

**📋 الوصف:**
صفحة عرض تفاصيل طلب محدد مع إمكانيات الإدارة.

**🧩 المكونات المطلوبة:**

#### Order Header
```typescript
interface AdminOrderHeaderProps {
  order: Order;
  onUpdateStatus: (status: OrderStatus) => void;
  onPrintInvoice: () => void;
  onSendEmail: (template: string) => void;
}
```
- **Order Information:** معلومات الطلب
- **Status Controls:** أدوات تحكم الحالة
- **Action Buttons:** أزرار الإجراءات
- **Order Timeline:** جدول زمني للطلب

#### Customer Information
```typescript
interface CustomerInfoProps {
  customer: Customer;
  onViewCustomer: () => void;
}
```
- **Customer Details:** تفاصيل العميل
- **Contact Information:** معلومات الاتصال
- **Order History:** تاريخ الطلبات
- **Customer Notes:** ملاحظات العميل

#### Order Items Management
```typescript
interface OrderItemsManagementProps {
  items: OrderItem[];
  onEditItem: (itemId: string, changes: ItemChanges) => void;
  onRemoveItem: (itemId: string) => void;
  onAddItem: (item: OrderItem) => void;
}
```
- **Items List:** قائمة المنتجات
- **Edit Quantities:** تعديل الكميات
- **Update Prices:** تحديث الأسعار
- **Add Products:** إضافة منتجات
- **Calculate Totals:** حساب الإجماليات

#### Shipping Management
```typescript
interface ShippingManagementProps {
  shipping: ShippingInfo;
  onUpdateShipping: (shipping: ShippingInfo) => void;
  onGenerateLabel: () => void;
  onTrackShipment: () => void;
}
```
- **Shipping Address:** عنوان الشحن
- **Shipping Method:** طريقة الشحن
- **Tracking Information:** معلومات التتبع
- **Shipping Labels:** ملصقات الشحن

#### Payment Management
```typescript
interface PaymentManagementProps {
  payment: PaymentInfo;
  onProcessRefund: (amount: number) => void;
  onCapturePayment: () => void;
}
```
- **Payment Details:** تفاصيل الدفع
- **Transaction History:** تاريخ المعاملات
- **Refund Options:** خيارات الاسترداد
- **Payment Actions:** إجراءات الدفع

#### Order Notes
```typescript
interface OrderNotesProps {
  notes: OrderNote[];
  onAddNote: (note: string, isPrivate: boolean) => void;
  onEditNote: (noteId: string, note: string) => void;
}
```
- **Internal Notes:** ملاحظات داخلية
- **Customer Notes:** ملاحظات العميل
- **System Logs:** سجلات النظام
- **Add Note Form:** نموذج إضافة ملاحظة

---

## 🔍 صفحات البحث والفلترة

### 19. صفحة نتائج البحث (`/search`)

**📋 الوصف:**
صفحة عرض نتائج البحث مع إمكانيات الفلترة المتقدمة.

**🧩 المكونات المطلوبة:**

#### Search Header
```typescript
interface SearchHeaderProps {
  query: string;
  resultsCount: number;
  searchTime: number;
  onNewSearch: (query: string) => void;
}
```
- **Search Query Display:** عرض استعلام البحث
- **Results Count:** عدد النتائج
- **Search Time:** وقت البحث
- **Search Suggestions:** اقتراحات البحث
- **Did You Mean:** هل تقصد

#### Search Filters
```typescript
interface SearchFiltersProps {
  filters: SearchFilters;
  facets: SearchFacets;
  onFilterChange: (filters: SearchFilters) => void;
}
```
- **Category Facets:** فئات النتائج
- **Price Facets:** نطاقات الأسعار
- **Brand Facets:** العلامات التجارية
- **Rating Facets:** التقييمات
- **Availability Facets:** التوفر

#### Search Results
```typescript
interface SearchResultsProps {
  results: SearchResult[];
  viewMode: 'grid' | 'list';
  sortBy: string;
  onSortChange: (sortBy: string) => void;
}
```
- **Results Grid/List:** شبكة/قائمة النتائج
- **Product Cards:** بطاقات المنتجات
- **Relevance Scoring:** نقاط الصلة
- **Highlighted Terms:** المصطلحات المميزة

#### No Results State
```typescript
interface NoResultsProps {
  query: string;
  suggestions: string[];
  popularProducts: Product[];
}
```
- **No Results Message:** رسالة عدم وجود نتائج
- **Search Suggestions:** اقتراحات البحث
- **Popular Products:** منتجات شائعة
- **Spelling Corrections:** تصحيحات إملائية

---

## 📄 الصفحات الثابتة والمحتوى

### 20. الصفحات الثابتة (`/pages/[slug]`)

**📋 الوصف:**
صفحات المحتوى الثابت مثل سياسة الخصوصية والشروط.

**🧩 المكونات المطلوبة:**

#### Page Layout
```typescript
interface StaticPageProps {
  page: StaticPage;
  relatedPages?: StaticPage[];
}
```
- **Page Title:** عنوان الصفحة
- **Page Content:** محتوى الصفحة
- **Table of Contents:** جدول المحتويات
- **Last Updated:** آخر تحديث
- **Related Pages:** صفحات ذات صلة

#### Content Sections
- **Rich Text Content:** محتوى نصي غني
- **Images and Media:** صور ووسائط
- **Lists and Tables:** قوائم وجداول
- **Call-to-Action:** دعوات للعمل

#### Common Static Pages
- **About Us:** من نحن
- **Privacy Policy:** سياسة الخصوصية
- **Terms of Service:** شروط الخدمة
- **Return Policy:** سياسة الإرجاع
- **Shipping Info:** معلومات الشحن
- **Size Guide:** دليل المقاسات
- **FAQ:** الأسئلة الشائعة
- **Contact Us:** اتصل بنا

---

## 📊 صفحات التقارير والتحليلات

### 21. صفحة التقارير (`/admin/reports`)

**📋 الوصف:**
صفحة عرض التقارير والتحليلات التفصيلية للمتجر.

**🧩 المكونات المطلوبة:**

#### Reports Dashboard
```typescript
interface ReportsDashboardProps {
  reportTypes: ReportType[];
  onGenerateReport: (type: string, params: ReportParams) => void;
}
```
- **Report Categories:** فئات التقارير
- **Quick Reports:** تقارير سريعة
- **Custom Reports:** تقارير مخصصة
- **Scheduled Reports:** تقارير مجدولة

#### Sales Reports
- **Sales Overview:** نظرة عامة على المبيعات
- **Product Performance:** أداء المنتجات
- **Customer Analytics:** تحليلات العملاء
- **Revenue Trends:** اتجاهات الإيرادات

#### Inventory Reports
- **Stock Levels:** مستويات المخزون
- **Low Stock Alerts:** تنبيهات المخزون المنخفض
- **Product Movement:** حركة المنتجات
- **Inventory Valuation:** تقييم المخزون

---

هذا التفصيل الشامل يغطي جميع صفحات الواجهة الأمامية مع المكونات والوظائف المطلوبة لكل صفحة. كل صفحة مصممة لتوفير تجربة مستخدم متميزة مع الحفاظ على الأداء والأمان.
